import os
import requests # Still used for synchronous model calls, can be kept for simplicity
import asyncio
import random
import aiohttp
import trafilatura
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from app.utils.model_initializer import model
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- MODIFICATION: Global ThreadPoolExecutor ---
# A single executor for all blocking tasks ensures efficient thread management.
executor = ThreadPoolExecutor(max_workers=15)


def extract_source_from_url(url: str) -> str:
    """
    Extract the source name from a URL.
    
    Args:
        url: The URL to extract source from
        
    Returns:
        The source name (e.g., "TechCrunch", "Reuters", "CNN")
    """
    try:
        if not url:
            return "Unknown Source"
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove common prefixes
        domain = domain.replace('www.', '')
        
        # Common news source mappings
        source_mappings = {
            'techcrunch.com': 'TechCrunch',
            'reuters.com': 'Reuters',
            'cnn.com': 'CNN',
            'bbc.com': 'BBC',
            'bbc.co.uk': 'BBC',
            'nytimes.com': 'The New York Times',
            'wsj.com': 'The Wall Street Journal',
            'washingtonpost.com': 'The Washington Post',
            'forbes.com': 'Forbes',
            'bloomberg.com': 'Bloomberg',
            'cnbc.com': 'CNBC',
            'theverge.com': 'The Verge',
            'arstechnica.com': 'Ars Technica',
            'wired.com': 'Wired',
            'engadget.com': 'Engadget',
            'mashable.com': 'Mashable',
            'venturebeat.com': 'VentureBeat',
            'zdnet.com': 'ZDNet',
            'cnet.com': 'CNET',
            'techradar.com': 'TechRadar',
            'theguardian.com': 'The Guardian',
            'independent.co.uk': 'The Independent',
            'ft.com': 'Financial Times',
            'economist.com': 'The Economist',
            'hbr.org': 'Harvard Business Review',
            'linkedin.com': 'LinkedIn',
            'medium.com': 'Medium',
            'substack.com': 'Substack',
            'github.com': 'GitHub',
            'stackoverflow.com': 'Stack Overflow',
            'reddit.com': 'Reddit',
            'twitter.com': 'Twitter',
            'x.com': 'X (Twitter)',
            'youtube.com': 'YouTube',
        }
        
        # Check if we have a direct mapping
        if domain in source_mappings:
            return source_mappings[domain]
        
        # Extract from domain name (capitalize and clean)
        if '.' in domain:
            # Get the main part of the domain (before the first dot)
            main_part = domain.split('.')[0]
            # Capitalize and clean
            source = main_part.replace('-', ' ').replace('_', ' ').title()
            return source
        
        return "Unknown Source"
        
    except Exception as e:
        print(f"Error extracting source from URL {url}: {str(e)}")
        return "Unknown Source"


def _convert_to_html_summary(summary: str) -> str:
    """
    Convert a plain text summary to HTML format with proper paragraph tags.
    If the summary is already HTML formatted, return as is.
    """
    # Check if already HTML formatted
    if '<p>' in summary.lower() and '</p>' in summary.lower():
        return summary

    # Clean up the summary text
    summary = summary.strip()

    # Remove any markdown-style formatting
    summary = summary.replace('**', '').replace('*', '')

    # Split into sentences and group them logically
    sentences = [s.strip() for s in summary.split('.') if s.strip()]

    # If we have sentences, wrap them in paragraph tags
    if sentences:
        # Rejoin sentences with periods and wrap in paragraph tags
        formatted_sentences = []
        for sentence in sentences:
            if sentence and not sentence.endswith('.'):
                sentence += '.'
            if sentence:
                formatted_sentences.append(f"<p>{sentence}</p>")

        return ''.join(formatted_sentences)
    else:
        # Fallback: split by newlines and wrap each line in paragraph tags
        lines = [line.strip() for line in summary.split('\n') if line.strip()]
        if lines:
            return ''.join([f"<p>{line}</p>" for line in lines])
        else:
            # Last resort: wrap the entire summary in a single paragraph
            return f"<p>{summary}</p>" if summary else "<p>Summary not available.</p>"


async def summarize_article(session: aiohttp.ClientSession, url: str, title: str = '', snippet: str = '') -> str:
    """
    Fetch the article content from the URL and generate a 2-3 line HTML-formatted summary.
    If content is insufficient, use title and snippet. If those are missing, use title alone.
    Always generate a meaningful summary from available information.
    Returns HTML-formatted content with proper paragraph tags.
    """
    summary_styles = [
        "Write a simple 2-3 line summary of this article for working professionals. Use easy words and focus on the main points and what they mean. Format the response as HTML with proper paragraph tags (<p></p>) for each sentence or logical group of sentences.",
        "Create a short 2-3 line summary of this article for business professionals. Use clear, simple language and highlight the main news. Format the response as HTML with proper paragraph tags (<p></p>) for each sentence or logical group of sentences.",
        "Give a brief 2-3 line overview of this article for LinkedIn professionals. Use everyday words and focus on practical takeaways. Format the response as HTML with proper paragraph tags (<p></p>) for each sentence or logical group of sentences.",
        "Write a 2-3 line summary of this article for professionals. Use simple words and focus on how this affects the industry. Format the response as HTML with proper paragraph tags (<p></p>) for each sentence or logical group of sentences.",
        "Summarize this article in 2-3 lines for working professionals. Use clear language and highlight trends and opportunities. Format the response as HTML with proper paragraph tags (<p></p>) for each sentence or logical group of sentences."
    ]
    
    prompt_template = random.choice(summary_styles)
    text_content = None
    loop = asyncio.get_running_loop()

    try:
        async with session.get(url, timeout=10) as response:
            response.raise_for_status()
            html_content = await response.text()
            # Offload CPU-bound parsing to executor
            text_content = await loop.run_in_executor(
                executor, trafilatura.extract, html_content
            )

        if text_content and len(text_content) >= 100:
            prompt = f"{prompt_template}\n\nArticle:\n{text_content[:4000]}"
        elif title or snippet:
            prompt = f"{prompt_template}\n\nTitle: {title}\nSnippet: {snippet}"
        elif title:
            prompt = f"{prompt_template}\n\nTitle: {title}"
        else:
            return "Summary not available for this article."

        # Offload blocking model call to executor
        blocking_call = partial(model.generate_content, prompt)
        response = await loop.run_in_executor(executor, blocking_call)
        summary = response.text.strip()

        # Convert to HTML format if not already formatted
        html_summary = _convert_to_html_summary(summary)
        return html_summary

    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        logger.warning(f"Error fetching article {url} for summary: {str(e)}. Falling back.")
        try:
            if title or snippet:
                prompt = f"{prompt_template}\n\nTitle: {title}\nSnippet: {snippet}\nURL: {url}"
                # Offload blocking model call to executor
                blocking_call = partial(model.generate_content, prompt)
                response = await loop.run_in_executor(executor, blocking_call)
                summary = response.text.strip()
                # Convert to HTML format if not already formatted
                html_summary = _convert_to_html_summary(summary)
                return html_summary
            else:
                return "<p>Summary not available for this article.</p>"
        except Exception as gen_e:
            logger.error(f"Fallback summary generation failed for {url}: {str(gen_e)}")
            return "<p>Summary not available for this article.</p>"
    except Exception as e:
        logger.error(f"An unexpected error occurred during summarization for {url}: {str(e)}")
        return "<p>Summary not available for this article.</p>"


@dataclass
class NewsArticle:
    """Data class for news articles"""
    url: str
    title: str
    snippet: str
    source: str
    published_at: Optional[str] = None
    summary: Optional[str] = None
    is_relevant: bool = True


@dataclass
class Category:
    """Data class for news categories"""
    name: str
    keywords: List[str]
    articles: List[NewsArticle] = field(default_factory=list)


class CategoryGeneratorAgent:
    """Agent responsible for generating up to 5 categories based on persona keywords"""
    
    def __init__(self):
        self.model = model
    
    async def generate_categories(self, 
                                general_persona_keywords: List[str],
                                content_persona_keywords: Optional[List[str]] = None,
                                network_persona_keywords: Optional[List[str]] = None) -> List[Category]:
        """
        Generate up to 5 unique categories, each composed of 2-3 words.
        Categories should be interesting, attention-grabbing, well-composed, and unique.
        """
        try:
            all_keywords = general_persona_keywords.copy()
            if content_persona_keywords:
                all_keywords.extend(content_persona_keywords)
            if network_persona_keywords:
                all_keywords.extend(network_persona_keywords)
            
            persona_description = f"Professional with keywords: {', '.join(all_keywords)}"
            
            prompt = f"""
You are a professional news category generator. Based on the following persona, generate exactly 5 unique news categories:
Persona: {persona_description}
Requirements:
1. Generate exactly 5 categories, each made of 2-3 words
2. Categories should be interesting, eye-catching, well-written, and unique
3. Each category should fit the person's work context and interests
4. Categories should be specific enough to find relevant news but broad enough to get results
5. Focus on work-related, industry-relevant categories
6. Avoid basic categories like "Technology News" or "Business Updates"
7. Make categories sound like professional LinkedIn content categories
8. Use simple, clear words that all professionals can understand
Format your response as a numbered list of exactly 5 categories, one per line.
"""
            
            # Offload blocking model call to executor
            loop = asyncio.get_running_loop()
            blocking_call = partial(self.model.generate_content, prompt)
            response = await loop.run_in_executor(executor, blocking_call)
            categories_text = response.text.strip()
            
            category_names = [line.split('.', 1)[1].strip() for line in categories_text.split('\n') if line and '.' in line]
            
            if len(category_names) != 5:
                raise ValueError("AI did not generate exactly 5 categories.")
            
            keyword_tasks = [self._generate_keywords_for_category(name, all_keywords) for name in category_names[:5]]
            all_keywords_for_categories = await asyncio.gather(*keyword_tasks)
            
            return [Category(name=name, keywords=keywords) for name, keywords in zip(category_names[:5], all_keywords_for_categories)]

        except Exception as e:
            logger.error(f"Error generating categories, falling back to defaults: {str(e)}")
            primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
            fallback_names = [f"{primary_keyword} Innovation", f"{primary_keyword} Leadership", f"{primary_keyword} Trends", f"{primary_keyword} Development", f"{primary_keyword} Insights"]
            return [Category(name=name, keywords=[name.lower(), "news"]) for name in fallback_names]
    
    async def _generate_keywords_for_category(self, category_name: str, persona_keywords: List[str]) -> List[str]:
        """Generate relevant keywords for a specific category with enhanced targeting"""
        try:
            # Create a more detailed prompt for better keyword generation
            prompt = f"""
Generate 4-6 highly specific and relevant keywords for the news category "{category_name}" based on these persona keywords: {', '.join(persona_keywords)}

Requirements:
1. Keywords must be directly related to the category and persona
2. Use technical terms and industry-specific language when appropriate
3. Include both broad and specific terms
4. Focus on terms that would appear in relevant news articles
5. Avoid generic terms like "news", "latest", "updates"

Examples of good keywords for different categories:
- For "Android Development": android, kotlin, jetpack compose, mobile app, google play
- For "Mobile Architecture": mvvm, clean architecture, dependency injection, mobile patterns
- For "UI/UX Design": user interface, user experience, design systems, accessibility

Return only the keywords, separated by commas, no explanations.
"""

            # Offload blocking model call to executor
            loop = asyncio.get_running_loop()
            blocking_call = partial(self.model.generate_content, prompt)
            response = await loop.run_in_executor(executor, blocking_call)
            keywords_text = response.text.strip()
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

            # Ensure we have quality keywords
            if len(keywords) >= 3:
                return keywords[:6]  # Return up to 6 keywords
            else:
                # Fallback with more targeted keywords based on category name
                fallback_keywords = self._generate_fallback_keywords(category_name, persona_keywords)
                return fallback_keywords

        except Exception as e:
            logger.error(f"Error generating keywords for category {category_name}: {str(e)}")
            return self._generate_fallback_keywords(category_name, persona_keywords)

    def _generate_fallback_keywords(self, category_name: str, persona_keywords: List[str]) -> List[str]:
        """Generate fallback keywords when AI generation fails"""
        keywords = [category_name.lower()]

        # Add relevant persona keywords
        for keyword in persona_keywords[:3]:
            if keyword.lower() not in keywords:
                keywords.append(keyword.lower())

        # Add category-specific terms
        category_lower = category_name.lower()
        if 'android' in category_lower:
            keywords.extend(['android', 'kotlin', 'mobile app'])
        elif 'mobile' in category_lower:
            keywords.extend(['mobile', 'app', 'smartphone'])
        elif 'ui' in category_lower or 'ux' in category_lower:
            keywords.extend(['ui', 'ux', 'design', 'interface'])
        elif 'architecture' in category_lower:
            keywords.extend(['architecture', 'design patterns', 'software design'])
        elif 'kotlin' in category_lower:
            keywords.extend(['kotlin', 'android', 'programming'])
        else:
            keywords.extend(['technology', 'development'])

        return list(set(keywords))[:5]  # Remove duplicates and limit


class NewsRetrievalAgent:
    """Agent responsible for keyword extraction and news retrieval using Tavily API"""

    def __init__(self, tavily_key: str, session: aiohttp.ClientSession):
        self.tavily_key = tavily_key
        self.session = session
    
    async def fetch_news_for_category(self, category: Category,
                                     persona_keywords: Optional[List[str]] = None) -> Tuple[List[NewsArticle], bool]:
        """
        Fetch recent news articles for a category using Tavily API.
        Returns (articles, success_flag) where success_flag indicates if relevant news was found.
        """
        if not self.tavily_key:
            return [], False

        try:
            # Fetch more articles initially to have a better pool after filtering
            articles = await self._fetch_from_tavily(category, 15)

            validation_tasks = [self._validate_relevance(article, category, persona_keywords) for article in articles]
            relevance_results = await asyncio.gather(*validation_tasks)

            relevant_articles = [article for article, is_relevant in zip(articles, relevance_results) if is_relevant]

            success = len(relevant_articles) > 0 # Success is finding at least one article
            return relevant_articles, success  # Return all relevant articles, let caller limit

        except Exception as e:
            logger.error(f"Error fetching news for category {category.name}: {str(e)}")
            return [], False

    async def _fetch_from_tavily(self, category: Category, count: int) -> List[NewsArticle]:
        """Fetch news articles from Tavily API with flexible domain strategy."""
        try:
            from tavily import TavilyClient
            client = TavilyClient(api_key=self.tavily_key)

            # A single, more comprehensive query is more efficient than multiple queries.
            query_parts = [category.name] + category.keywords[:2]
            comprehensive_query = f"latest news and recent developments on {' '.join(query_parts)}"

            logger.info(f"Performing consolidated Tavily search for: {comprehensive_query}")

            loop = asyncio.get_running_loop()

            # First attempt: Try preferred/trusted domains
            preferred_domains = ["reuters.com", "cnn.com", "bbc.com", "techcrunch.com", "bloomberg.com", "forbes.com", "wsj.com", "nytimes.com", "theverge.com", "wired.com"]

            blocking_search_preferred = partial(
                client.search,
                query=comprehensive_query,
                search_depth="basic",
                topic="news",  # CRITICAL: Required for published_date field
                days=14,  # Extended from 7 to 14 days for more articles
                max_results=15,  # Fetch more to have a better pool to filter from
                include_domains=preferred_domains
            )
            response = await loop.run_in_executor(executor, blocking_search_preferred)

            all_articles = []
            for result in response.get('results', []):
                article = NewsArticle(
                    url=result.get('url', ''),
                    title=result.get('title', ''),
                    snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                    source=extract_source_from_url(result.get('url', '')),
                    # --- DATE FIX: Use the correct key 'published_date' from Tavily API ---
                    published_at=result.get('published_date', None)
                )
                all_articles.append(article)

            # If insufficient articles from preferred domains, expand search to all sources
            if len(all_articles) < count:
                logger.info(f"Only found {len(all_articles)} articles from preferred domains, expanding search to all sources")

                blocking_search_expanded = partial(
                    client.search,
                    query=comprehensive_query,
                    search_depth="basic",
                    topic="news",
                    days=14,  # Same 14-day range
                    max_results=20  # Fetch even more from all sources
                    # No include_domains parameter = search all sources
                )
                expanded_response = await loop.run_in_executor(executor, blocking_search_expanded)

                # Add articles from expanded search
                for result in expanded_response.get('results', []):
                    article = NewsArticle(
                        url=result.get('url', ''),
                        title=result.get('title', ''),
                        snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                        source=extract_source_from_url(result.get('url', '')),
                        published_at=result.get('published_date', None)
                    )
                    all_articles.append(article)

            # Remove duplicates - this remains a good practice
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url and article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)

            return unique_articles[:count]

        except Exception as e:
            logger.error(f"Error in _fetch_from_tavily: {str(e)}")
            return []

    async def _validate_relevance(self, article: NewsArticle, category: Category,
                                  persona_keywords: Optional[List[str]] = None) -> bool:
        """
        Enhanced validation that checks relevance against both category and persona keywords.
        Uses multi-level filtering for better accuracy.
        """
        article_text = f"{article.title.lower()} {article.snippet.lower()}"

        # Level 1: Category keyword matching (basic relevance)
        category_terms = [category.name.lower()] + [kw.lower() for kw in category.keywords]
        category_score = sum(1 for term in category_terms if term in article_text)

        # Level 2: Persona keyword matching (domain relevance)
        persona_score = 0
        if persona_keywords:
            persona_terms = [kw.lower() for kw in persona_keywords]
            persona_score = sum(1 for term in persona_terms if term in article_text)

            # Check for partial matches (e.g., "android" in "Android Development")
            for term in persona_terms:
                if len(term) > 3:  # Only check meaningful terms
                    for word in term.split():
                        if len(word) > 3 and word in article_text:
                            persona_score += 0.5

        # Level 3: Technology/domain-specific terms
        tech_terms = self._extract_tech_terms(persona_keywords) if persona_keywords else []
        tech_score = sum(1 for term in tech_terms if term.lower() in article_text)

        # Weighted scoring system
        total_score = (category_score * 1.0) + (persona_score * 2.0) + (tech_score * 1.5)

        # Higher threshold for better relevance
        min_threshold = 3.0

        # Bonus for exact category name match
        if category.name.lower() in article_text:
            total_score += 2.0

        # Penalty for clearly irrelevant content
        irrelevant_terms = [
            'construction', 'hospitality', 'restaurant', 'hotel', 'real estate',
            'military', 'defense', 'weapon', 'politics', 'election', 'government policy',
            'sports', 'entertainment', 'celebrity', 'music', 'movie', 'tv show',
            'cryptocurrency scam', 'financial fraud', 'layoffs', 'job cuts'
        ]

        penalty = sum(2 for term in irrelevant_terms if term in article_text)
        total_score -= penalty

        return total_score >= min_threshold

    def _extract_tech_terms(self, persona_keywords: List[str]) -> List[str]:
        """Extract technology-specific terms from persona keywords"""
        tech_terms = []
        for keyword in persona_keywords:
            keyword_lower = keyword.lower()
            # Add specific technology terms
            if 'android' in keyword_lower:
                tech_terms.extend(['android', 'kotlin', 'java', 'jetpack', 'compose'])
            elif 'mobile' in keyword_lower:
                tech_terms.extend(['mobile', 'app', 'ios', 'flutter', 'react native'])
            elif 'kotlin' in keyword_lower:
                tech_terms.extend(['kotlin', 'android', 'multiplatform'])
            elif 'java' in keyword_lower:
                tech_terms.extend(['java', 'jvm', 'spring'])
            elif 'ui' in keyword_lower or 'ux' in keyword_lower:
                tech_terms.extend(['ui', 'ux', 'design', 'interface', 'user experience'])
            elif 'api' in keyword_lower:
                tech_terms.extend(['api', 'rest', 'graphql', 'endpoint'])
            elif 'firebase' in keyword_lower:
                tech_terms.extend(['firebase', 'google cloud', 'backend'])
            elif 'performance' in keyword_lower:
                tech_terms.extend(['performance', 'optimization', 'speed', 'efficiency'])

        return list(set(tech_terms))  # Remove duplicates


class FallbackAgent:
    """Agent responsible for a single broader search when initial retrieval fails completely"""

    def __init__(self, tavily_key: str, session: aiohttp.ClientSession):
        self.tavily_key = tavily_key
        self.session = session
    
    async def fetch_fallback_news(self, category: Category,
                                 persona_keywords: Optional[List[str]] = None) -> List[NewsArticle]:
        """Perform a single, broader search to find relevant news using Tavily API."""
        if not self.tavily_key:
            return []

        try:
            from tavily import TavilyClient
            client = TavilyClient(api_key=self.tavily_key)

            # Generate more targeted fallback queries using persona keywords
            broader_queries = self._generate_broader_queries(category, persona_keywords)
            if not broader_queries:
                return []

            fallback_query = broader_queries[0] # Use the first (best) one
            logger.info(f"Performing single fallback Tavily search for: {fallback_query}")

            loop = asyncio.get_running_loop()
            # Offload the blocking Tavily search to the executor
            blocking_search = partial(
                client.search,
                query=f"{fallback_query} recent news",
                search_depth="basic",
                topic="news",  # CRITICAL: Required for published_date field
                days=14,  # Changed from 30 to 14 days to match primary search
                max_results=10  # Increased from 5 to get more options
            )
            response = await loop.run_in_executor(executor, blocking_search)

            all_articles = []
            for result in response.get('results', []):
                article = NewsArticle(
                    url=result.get('url', ''),
                    title=result.get('title', ''),
                    snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                    source=extract_source_from_url(result.get('url', '')),
                    # --- DATE FIX: Use the correct key 'published_date' from Tavily API ---
                    published_at=result.get('published_date', None)
                )
                all_articles.append(article)
            
            # Remove duplicates
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url and article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)

            return unique_articles[:5]

        except Exception as e:
            logger.error(f"Error in fallback news retrieval for {category.name}: {str(e)}")
            return []

    def _generate_broader_queries(self, category: Category,
                                 persona_keywords: Optional[List[str]] = None) -> List[str]:
        """Generate more targeted broader search queries using persona keywords"""
        base_terms = [category.name] + category.keywords[:2]

        # Add relevant persona keywords to base terms
        if persona_keywords:
            # Select the most relevant persona keywords (first 3)
            relevant_persona_terms = persona_keywords[:3]
            base_terms.extend(relevant_persona_terms)

        # More specific industry terms based on persona
        if persona_keywords and any('android' in kw.lower() for kw in persona_keywords):
            industry_terms = ["android development", "mobile app", "kotlin", "java", "mobile technology"]
        elif persona_keywords and any('mobile' in kw.lower() for kw in persona_keywords):
            industry_terms = ["mobile development", "app development", "mobile technology", "smartphone"]
        else:
            industry_terms = ["technology", "software development", "programming", "tech industry"]

        time_terms = ["latest", "recent", "new", "2024", "2025"]

        # Create more targeted queries
        broader_queries = []
        for base in base_terms[:3]:  # Limit base terms
            for term in industry_terms[:2]:  # Limit industry terms
                broader_queries.append(f"{base} {term}")
                broader_queries.append(f"{term} {base}")

        # Add time-based queries
        for base in base_terms[:2]:
            for time_term in time_terms[:2]:
                broader_queries.append(f"{time_term} {base}")

        # Remove duplicates and shuffle
        broader_queries = list(set(broader_queries))
        random.shuffle(broader_queries)
        return broader_queries[:5]  # Return more options


class SupervisorAgent:
    """Supervisor Agent that manages and coordinates all sub-agents"""

    def __init__(self, tavily_key: Optional[str]):
        self.tavily_key = tavily_key
        self.category_agent = CategoryGeneratorAgent()
        self.news_agent: Optional[NewsRetrievalAgent] = None
        self.fallback_agent: Optional[FallbackAgent] = None

    async def get_trending_news(self,
                               general_persona_keywords: List[str],
                               content_persona_keywords: Optional[List[str]] = None,
                               network_persona_keywords: Optional[List[str]] = None,
                               user_categories: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Main method that coordinates the entire agentic workflow.
        Returns the complete output formatted like the current /trending-news response.
        """
        async with aiohttp.ClientSession() as session:
            self.news_agent = NewsRetrievalAgent(self.tavily_key, session)
            self.fallback_agent = FallbackAgent(self.tavily_key, session)

            try:
                # Step 1: Generate or use categories
                if user_categories:
                    keyword_tasks = [self.category_agent._generate_keywords_for_category(name, general_persona_keywords) for name in user_categories[:5]]
                    keywords_list = await asyncio.gather(*keyword_tasks)
                    categories = [Category(name, keywords) for name, keywords in zip(user_categories[:5], keywords_list)]
                else:
                    categories = await self.category_agent.generate_categories(
                        general_persona_keywords, content_persona_keywords, network_persona_keywords)

                if not categories:
                    raise ValueError("Category generation failed.")

                # Step 2: Fetch news for all categories concurrently with persona keywords
                all_persona_keywords = general_persona_keywords.copy()
                if content_persona_keywords:
                    all_persona_keywords.extend(content_persona_keywords)
                if network_persona_keywords:
                    all_persona_keywords.extend(network_persona_keywords)

                fetch_tasks = [self._fetch_and_fallback_for_category(category, all_persona_keywords) for category in categories]
                category_article_results = await asyncio.gather(*fetch_tasks)

                # Step 3: Collect and deduplicate all articles before summarization
                unique_articles_map: Dict[str, NewsArticle] = {}
                for category, articles in zip(categories, category_article_results):
                    category.articles = articles
                    for article in articles:
                        if article.url not in unique_articles_map:
                            unique_articles_map[article.url] = article
                
                # Step 4: Summarize all unique articles concurrently
                summary_tasks = [summarize_article(session, article.url, article.title, article.snippet)
                                 for article in unique_articles_map.values()]
                summaries = await asyncio.gather(*summary_tasks)
                
                # Add summaries to the unique articles
                for article, summary in zip(unique_articles_map.values(), summaries):
                    article.summary = summary
                
                # Step 5: Build the final response
                final_results = []
                for category in categories:
                    summarized_articles_for_cat = [unique_articles_map[art.url] for art in category.articles if art.url in unique_articles_map]
                    
                    # We only need one good article to form a category now
                    if summarized_articles_for_cat:
                        final_results.append({
                            "category_name": category.name,
                            "articles": [{
                                "url": art.url, "title": art.title, "published_at": art.published_at,
                                "summary": art.summary, "source": art.source
                            } for art in summarized_articles_for_cat[:5]] # Limit to 5
                        })

                logger.info(f"Final result: {len(final_results)} categories with news")
                return {"news": final_results}

            except Exception as e:
                logger.error(f"Error in SupervisorAgent: {str(e)}")
                primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
                return {
                    "news": [{
                        "category_name": f"{primary_keyword} News",
                        "articles": [{
                            "url": f"https://example.com/news/{primary_keyword.lower()}-{i+1}",
                            "title": f"Latest {primary_keyword} News #{i+1}",
                            "summary": f"<p>Recent developments in {primary_keyword} industry.</p><p>This is a fallback summary with HTML formatting.</p>",
                            "source": "Example News"
                        } for i in range(5)]
                    }]
                }

    async def _fetch_and_fallback_for_category(self, category: Category,
                                              persona_keywords: Optional[List[str]] = None) -> List[NewsArticle]:
        """
        Helper to fetch news for a single category, ensuring minimum 3-5 articles per category.
        Uses progressive fallback strategies to meet minimum article requirements.
        """
        articles, success = await self.news_agent.fetch_news_for_category(category, persona_keywords)

        # Target: 3-5 articles per category (minimum 3, maximum 5)
        MIN_ARTICLES = 3
        MAX_ARTICLES = 5

        # If we have enough articles, return them (capped at max)
        if len(articles) >= MIN_ARTICLES:
            return articles[:MAX_ARTICLES]

        # If we have some articles but not enough, try to get more
        if len(articles) > 0:
            logger.info(f"Category {category.name} has {len(articles)} articles, need {MIN_ARTICLES}. Trying fallback for more.")
            fallback_articles = await self.fallback_agent.fetch_fallback_news(category, persona_keywords)

            # Combine and deduplicate
            combined_articles = articles.copy()
            seen_urls = {article.url for article in articles}

            for fallback_article in fallback_articles:
                if fallback_article.url not in seen_urls and len(combined_articles) < MAX_ARTICLES:
                    combined_articles.append(fallback_article)
                    seen_urls.add(fallback_article.url)

            # If still not enough, try broader search
            if len(combined_articles) < MIN_ARTICLES:
                logger.info(f"Still need more articles for {category.name}. Trying broader search.")
                broader_articles = await self._fetch_broader_articles(category, persona_keywords, seen_urls)
                combined_articles.extend(broader_articles[:MAX_ARTICLES - len(combined_articles)])

            return combined_articles[:MAX_ARTICLES]

        # If no articles from primary search, use fallback
        logger.warning(f"Initial retrieval failed for {category.name}, using fallback strategies.")
        fallback_articles = await self.fallback_agent.fetch_fallback_news(category, persona_keywords)

        # If fallback also insufficient, try broader search
        if len(fallback_articles) < MIN_ARTICLES:
            logger.info(f"Fallback returned {len(fallback_articles)} articles, trying broader search.")
            seen_urls = {article.url for article in fallback_articles}
            broader_articles = await self._fetch_broader_articles(category, persona_keywords, seen_urls)
            fallback_articles.extend(broader_articles[:MAX_ARTICLES - len(fallback_articles)])

        return fallback_articles[:MAX_ARTICLES]

    async def _fetch_broader_articles(self, category: Category,
                                     persona_keywords: Optional[List[str]] = None,
                                     seen_urls: Optional[set] = None) -> List[NewsArticle]:
        """
        Fetch articles using broader, more generic search terms when specific searches fail.
        """
        if seen_urls is None:
            seen_urls = set()

        try:
            from tavily import TavilyClient
            client = TavilyClient(api_key=self.tavily_key)

            # Create broader search queries
            broader_queries = []

            # Use category name with generic terms
            broader_queries.append(f"{category.name} news")
            broader_queries.append(f"{category.name} updates")

            # Use persona keywords with generic terms
            if persona_keywords:
                for keyword in persona_keywords[:2]:  # Use first 2 persona keywords
                    broader_queries.append(f"{keyword} news")
                    broader_queries.append(f"{keyword} industry")

            # Try each broader query until we get some results
            all_broader_articles = []
            loop = asyncio.get_running_loop()

            for query in broader_queries[:3]:  # Limit to 3 queries to avoid too many API calls
                try:
                    blocking_search = partial(
                        client.search,
                        query=query,
                        search_depth="basic",
                        topic="news",
                        days=14,
                        max_results=5
                    )
                    response = await loop.run_in_executor(executor, blocking_search)

                    for result in response.get('results', []):
                        if result.get('url') not in seen_urls:
                            article = NewsArticle(
                                url=result.get('url', ''),
                                title=result.get('title', ''),
                                snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                                source=extract_source_from_url(result.get('url', '')),
                                published_at=result.get('published_date', None)
                            )
                            all_broader_articles.append(article)
                            seen_urls.add(article.url)

                            # Stop if we have enough articles
                            if len(all_broader_articles) >= 5:
                                break

                    if len(all_broader_articles) >= 3:
                        break

                except Exception as e:
                    logger.warning(f"Error in broader search with query '{query}': {str(e)}")
                    continue

            return all_broader_articles

        except Exception as e:
            logger.error(f"Error in _fetch_broader_articles: {str(e)}")
            return []


# Global supervisor instance initialization
TAVILY_API_KEY = os.environ.get("TAVILY_API_KEY")
if not TAVILY_API_KEY:
    print("Warning: TAVILY_API_KEY environment variable not set. News retrieval will be disabled.")
supervisor = SupervisorAgent(tavily_key=TAVILY_API_KEY)


async def get_trending_news_agentic(general_persona_keywords: List[str],
                                   content_persona_keywords: Optional[List[str]] = None,
                                   network_persona_keywords: Optional[List[str]] = None,
                                   categories: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Main function to get trending news using the agentic architecture.
    """
    return await supervisor.get_trending_news(
        general_persona_keywords=general_persona_keywords,
        content_persona_keywords=content_persona_keywords,
        network_persona_keywords=network_persona_keywords,
        user_categories=categories
    )