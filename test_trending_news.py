#!/usr/bin/env python3
"""
Test script to verify the trending news endpoint returns HTML-formatted summaries.
"""

import requests
import json

def test_trending_news_endpoint():
    """Test the /trending-news endpoint and verify HTML formatting in summaries."""
    
    # API endpoint
    url = "http://127.0.0.1:8001/trending-news"
    
    # Test payload
    payload = {
        "general_persona_keywords": ["technology", "AI", "software development"],
        "content_persona_keywords": ["machine learning", "artificial intelligence"],
        "network_persona_keywords": ["tech professionals", "developers"],
        "categories": ["Technology", "AI"]
    }
    
    try:
        print("Making request to trending news endpoint...")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        print("-" * 50)
        
        # Make the request
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            data = response.json()
            print("Response received successfully!")
            print(f"Response structure: {json.dumps(data, indent=2)}")
            
            # Check if news data exists
            if "news" in data and data["news"]:
                print("\n" + "="*60)
                print("ANALYZING SUMMARY FORMATTING:")
                print("="*60)
                
                for category in data["news"]:
                    print(f"\nCategory: {category['category_name']}")
                    print("-" * 40)
                    
                    for i, article in enumerate(category["articles"], 1):
                        print(f"\nArticle {i}:")
                        print(f"Title: {article['title']}")
                        print(f"Source: {article['source']}")
                        print(f"URL: {article['url']}")
                        print(f"Summary: {article['summary']}")
                        
                        # Check if summary contains HTML tags
                        summary = article['summary']
                        has_html = '<p>' in summary and '</p>' in summary
                        print(f"Contains HTML formatting: {'✓ YES' if has_html else '✗ NO'}")
                        
                        if has_html:
                            print("✓ Summary is properly HTML formatted!")
                        else:
                            print("✗ Summary is plain text, not HTML formatted")
                        
                        print("-" * 30)
                
            else:
                print("No news data found in response")
                
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except json.JSONDecodeError as e:
        print(f"Failed to parse JSON response: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_trending_news_endpoint()
